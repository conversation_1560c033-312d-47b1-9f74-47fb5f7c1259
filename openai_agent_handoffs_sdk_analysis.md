# OpenAI Agent SDK Analysis - handoffs.py

## Table of Contents
1. [Classes Overview](#classes-overview)
2. [Inheritance Structure](#inheritance-structure)
3. [Detailed Class Analysis](#detailed-class-analysis)
4. [Parameter Requirements & Error Analysis](#parameter-requirements--error-analysis)
5. [Type Aliases & Functions](#type-aliases--functions)

## Classes Overview

| Class Name | Type | Purpose | Required Parameters | Optional Parameters |
|------------|------|---------|-------------------|-------------------|
| `HandoffInputData` | @dataclass (frozen) | Container for handoff input data | `input_history`, `pre_handoff_items`, `new_items` | None |
| `Handoff[TContext]` | @dataclass, Generic | Agent delegation configuration | `tool_name`, `tool_description`, `input_json_schema`, `on_invoke_handoff`, `agent_name` | `input_filter`, `strict_json_schema`, `is_enabled` |

## Inheritance Structure

```
Generic[TContext]
    └── Handoff[TContext]

@dataclass(frozen=True)
    └── HandoffInputData

TypeAlias
    └── HandoffInputFilter

Function Overloads
    └── handoff() (3 overloads)
```

## Detailed Class Analysis

### 1. HandoffInputData
**Type**: `@dataclass(frozen=True)`
**Purpose**: Immutable container for conversation data passed during handoffs

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `input_history` | `str \| tuple[TResponseInputItem, ...]` | ✅ Yes | None | Input history before Runner.run() |
| `pre_handoff_items` | `tuple[RunItem, ...]` | ✅ Yes | None | Items generated before handoff turn |
| `new_items` | `tuple[RunItem, ...]` | ✅ Yes | None | Items generated during current turn |

**Potential Errors**:
- `TypeError`: If any required parameter is missing or wrong type
- `FrozenInstanceError`: If attempting to modify after creation (frozen dataclass)

### 2. Handoff[TContext]
**Type**: `@dataclass`, `Generic[TContext]`
**Purpose**: Configuration for agent-to-agent delegation with tool representation

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `tool_name` | `str` | ✅ Yes | None | Name of handoff tool for LLM |
| `tool_description` | `str` | ✅ Yes | None | Description of handoff tool |
| `input_json_schema` | `dict[str, Any]` | ✅ Yes | None | JSON schema for handoff input |
| `on_invoke_handoff` | `Callable[[RunContextWrapper[Any], str], Awaitable[Agent[TContext]]]` | ✅ Yes | None | Handoff invocation function |
| `agent_name` | `str` | ✅ Yes | None | Name of target agent |
| `input_filter` | `HandoffInputFilter \| None` | ❌ No | `None` | Input filtering function |
| `strict_json_schema` | `bool` | ❌ No | `True` | Enable strict JSON schema mode |
| `is_enabled` | `bool \| Callable` | ❌ No | `True` | Handoff enabled state or function |

**Potential Errors**:
- `TypeError`: Missing required parameters
- `ModelBehaviorError`: Invalid JSON input during handoff
- `UserError`: Invalid on_handoff function signature
- `ValidationError`: Input validation failures

### Key Methods:
| Method | Purpose | Parameters | Return Type |
|--------|---------|------------|-------------|
| `get_transfer_message()` | Generate transfer message | `agent: Agent[Any]` | `str` |
| `default_tool_name()` | Generate default tool name | `agent: Agent[Any]` | `str` |
| `default_tool_description()` | Generate default description | `agent: Agent[Any]` | `str` |

## Parameter Requirements & Error Analysis

### Critical Required Parameters:
1. **HandoffInputData**: All 3 parameters required (immutable container)
2. **Handoff**: 5 required parameters (tool configuration)

### Common Error Scenarios:

#### Handoff Creation Errors:
1. **Missing agent**: `TypeError` if agent parameter not provided to handoff() function
2. **Invalid on_handoff signature**: `UserError` if function doesn't match expected signature
3. **Mismatched input_type**: `AssertionError` if on_handoff and input_type don't align
4. **Invalid JSON schema**: `ValidationError` during schema creation

#### Handoff Execution Errors:
1. **Missing input**: `ModelBehaviorError` when input expected but not provided
2. **Invalid JSON**: `ModelBehaviorError` when LLM provides malformed JSON
3. **Validation failure**: `ValidationError` when input doesn't match input_type
4. **Agent not found**: `RuntimeError` if target agent is unavailable

#### Input Filter Errors:
1. **Invalid filter function**: `TypeError` if input_filter is not callable
2. **Filter execution failure**: Various errors depending on filter implementation
3. **Invalid return type**: `TypeError` if filter doesn't return HandoffInputData

### Best Practices for Error Prevention:
1. Always provide valid Agent instance to handoff() function
2. Ensure on_handoff function signatures match expected patterns
3. Use proper type hints for input_type parameter
4. Validate input_filter functions before assignment
5. Handle async operations properly in on_handoff callbacks

## Type Aliases & Functions

### Type Aliases:
| Name | Type | Purpose |
|------|------|---------|
| `THandoffInput` | TypeVar | Generic type for handoff input data |
| `OnHandoffWithInput` | Callable | Handoff callback with input parameter |
| `OnHandoffWithoutInput` | Callable | Handoff callback without input |
| `HandoffInputFilter` | Callable | Function to filter handoff input data |

### Key Functions:
| Function | Purpose | Overloads | Return Type |
|----------|---------|-----------|-------------|
| `handoff()` | Create Handoff from Agent | 3 overloads | `Handoff[TContext]` |

#### handoff() Function Overloads:
1. **Basic handoff**: `handoff(agent, *, tool_name_override=None, ...)`
2. **With input**: `handoff(agent, *, on_handoff, input_type, ...)`
3. **Without input**: `handoff(agent, *, on_handoff, ...)`

## Handoff Creation Patterns

### 1. Basic Agent Handoff
```python
from agents import Agent, handoff

billing_agent = Agent(name="Billing Agent")
refund_agent = Agent(name="Refund Agent")

# Simple handoff - agent directly
triage_agent = Agent(
    name="Triage Agent",
    handoffs=[billing_agent, refund_agent]
)

# Using handoff() function for customization
triage_agent = Agent(
    name="Triage Agent", 
    handoffs=[handoff(billing_agent), handoff(refund_agent)]
)
```

### 2. Handoff with Custom Configuration
```python
custom_handoff = handoff(
    agent=billing_agent,
    tool_name_override="escalate_to_billing",
    tool_description_override="Transfer to billing specialist",
    input_filter=lambda data: filter_sensitive_info(data)
)
```

### 3. Handoff with Input Data
```python
from pydantic import BaseModel

class EscalationData(BaseModel):
    reason: str
    priority: Literal["low", "medium", "high"]

async def on_escalation(ctx: RunContextWrapper, data: EscalationData):
    print(f"Escalating with reason: {data.reason}, priority: {data.priority}")
    # Log escalation, update metrics, etc.

escalation_handoff = handoff(
    agent=escalation_agent,
    on_handoff=on_escalation,
    input_type=EscalationData
)
```

### 4. Handoff without Input Data
```python
async def on_handoff_callback(ctx: RunContextWrapper):
    print(f"Handing off to {agent.name}")
    # Perform setup, logging, etc.

simple_handoff = handoff(
    agent=target_agent,
    on_handoff=on_handoff_callback
)
```

### 5. Conditional Handoff Enablement
```python
def handoff_enabled(ctx: RunContextWrapper, agent: Agent) -> bool:
    # Enable handoff based on context
    return (
        ctx.context.user_tier == "premium" and
        ctx.context.has_permission("escalation")
    )

conditional_handoff = handoff(
    agent=premium_agent,
    is_enabled=handoff_enabled
)
```

## Input Filtering Patterns

### 1. Using Built-in Filters
```python
from agents.extensions import handoff_filters

# Remove all tool calls from history
clean_handoff = handoff(
    agent=faq_agent,
    input_filter=handoff_filters.remove_all_tools
)
```

### 2. Custom Input Filter
```python
def custom_filter(data: HandoffInputData) -> HandoffInputData:
    # Keep only last 5 items from history
    filtered_items = data.new_items[-5:] if len(data.new_items) > 5 else data.new_items
    
    return HandoffInputData(
        input_history=data.input_history,
        pre_handoff_items=data.pre_handoff_items,
        new_items=filtered_items
    )

filtered_handoff = handoff(
    agent=target_agent,
    input_filter=custom_filter
)
```

### 3. Security-Focused Filter
```python
def security_filter(data: HandoffInputData) -> HandoffInputData:
    # Remove sensitive information from conversation
    cleaned_items = []
    for item in data.new_items:
        if not contains_sensitive_data(item):
            cleaned_items.append(item)
    
    return HandoffInputData(
        input_history=data.input_history,
        pre_handoff_items=data.pre_handoff_items,
        new_items=tuple(cleaned_items)
    )
```

## Common Usage Patterns & Error Prevention

### 1. Customer Support Scenario
```python
# Triage agent with specialized handoffs
billing_agent = Agent(
    name="Billing Agent",
    instructions="Handle billing inquiries and payment issues"
)

technical_agent = Agent(
    name="Technical Agent", 
    instructions="Handle technical support requests"
)

escalation_agent = Agent(
    name="Escalation Agent",
    instructions="Handle escalated issues requiring manager attention"
)

triage_agent = Agent(
    name="Triage Agent",
    instructions="""
    You are a customer support triage agent. Analyze customer requests and 
    transfer to appropriate specialists:
    - Billing issues → Billing Agent
    - Technical problems → Technical Agent  
    - Complex/angry customers → Escalation Agent
    """,
    handoffs=[
        handoff(billing_agent),
        handoff(technical_agent),
        handoff(escalation_agent, input_type=EscalationData, on_handoff=log_escalation)
    ]
)
```

### 2. Multi-Step Workflow
```python
# Sequential handoffs for complex processes
data_collector = Agent(name="Data Collector")
data_processor = Agent(name="Data Processor") 
report_generator = Agent(name="Report Generator")

workflow_agent = Agent(
    name="Workflow Coordinator",
    handoffs=[
        handoff(data_collector, tool_name_override="collect_data"),
        handoff(data_processor, tool_name_override="process_data"),
        handoff(report_generator, tool_name_override="generate_report")
    ]
)

## Excel-Style Parameter Reference Table

| Class | Parameter | Type | Required | Default Value | Validation Rules | Error if Missing/Invalid |
|-------|-----------|------|----------|---------------|------------------|-------------------------|
| HandoffInputData | input_history | str\|tuple[TResponseInputItem,...] | ✅ | None | Valid string or tuple of input items | TypeError: missing required argument |
| HandoffInputData | pre_handoff_items | tuple[RunItem,...] | ✅ | None | Tuple of RunItem objects | TypeError: missing required argument |
| HandoffInputData | new_items | tuple[RunItem,...] | ✅ | None | Tuple of RunItem objects | TypeError: missing required argument |
| Handoff | tool_name | str | ✅ | None | Non-empty string | TypeError: missing required argument |
| Handoff | tool_description | str | ✅ | None | String description | TypeError: missing required argument |
| Handoff | input_json_schema | dict[str,Any] | ✅ | None | Valid JSON schema dict | TypeError: missing required argument |
| Handoff | on_invoke_handoff | Callable | ✅ | None | Async callable with correct signature | TypeError: missing required argument |
| Handoff | agent_name | str | ✅ | None | Non-empty string | TypeError: missing required argument |
| Handoff | input_filter | HandoffInputFilter\|None | ❌ | None | Valid filter function or None | TypeError on invalid function |
| Handoff | strict_json_schema | bool | ❌ | True | Boolean value | TypeError on non-boolean |
| Handoff | is_enabled | bool\|Callable | ❌ | True | Boolean or callable returning bool | TypeError on invalid type |
| handoff() | agent | Agent[TContext] | ✅ | None | Valid Agent instance | TypeError: missing required argument |
| handoff() | tool_name_override | str\|None | ❌ | None | Valid string or None | TypeError on invalid type |
| handoff() | tool_description_override | str\|None | ❌ | None | Valid string or None | TypeError on invalid type |
| handoff() | on_handoff | Callable\|None | ❌ | None | Valid callback function or None | UserError on invalid signature |
| handoff() | input_type | type\|None | ❌ | None | Valid type or None | TypeError on invalid type |
| handoff() | input_filter | HandoffInputFilter\|None | ❌ | None | Valid filter function or None | TypeError on invalid function |
| handoff() | is_enabled | bool\|Callable | ❌ | True | Boolean or callable returning bool | TypeError on invalid type |

## Handoff Workflow Comparison Table

| Handoff Type | Input Required | Callback | Use Case | Complexity | Error Handling |
|--------------|----------------|----------|----------|------------|----------------|
| Basic | ❌ No | ❌ No | Simple delegation | Low | Minimal |
| With Input | ✅ Yes | ✅ Yes | Data collection + delegation | Medium | Input validation |
| Without Input | ❌ No | ✅ Yes | Logging + delegation | Low | Callback errors |
| Filtered | ❌ No | ❌ No | Privacy-aware delegation | Medium | Filter errors |
| Conditional | ❌ No | ❌ No | Context-based delegation | Medium | Enablement logic |

## Object Relationship Mapping

| Parent Object | Child/Related Objects | Relationship Type | Usage Context |
|---------------|----------------------|-------------------|---------------|
| Handoff | Agent | Target | Delegation destination |
| Handoff | HandoffInputFilter | Association | Input processing |
| Handoff | RunContextWrapper | Usage | Execution context |
| Handoff | JSON Schema | Composition | Input validation |
| HandoffInputData | RunItem | Aggregation | Conversation history |
| HandoffInputData | TResponseInputItem | Aggregation | Input history |
| handoff() function | Agent | Parameter | Target agent |
| handoff() function | Handoff | Creation | Result object |

## Error Handling Strategies

### 1. Handoff Creation Validation
```python
def create_safe_handoff(agent: Agent, **kwargs) -> Handoff:
    """Create handoff with comprehensive validation."""
    try:
        # Validate agent
        if not isinstance(agent, Agent):
            raise TypeError("agent must be Agent instance")

        # Validate on_handoff signature if provided
        on_handoff = kwargs.get('on_handoff')
        input_type = kwargs.get('input_type')

        if on_handoff and input_type:
            sig = inspect.signature(on_handoff)
            if len(sig.parameters) != 2:
                raise UserError("on_handoff with input_type must take 2 parameters")
        elif on_handoff:
            sig = inspect.signature(on_handoff)
            if len(sig.parameters) != 1:
                raise UserError("on_handoff without input_type must take 1 parameter")

        return handoff(agent, **kwargs)

    except Exception as e:
        logger.error(f"Failed to create handoff: {e}")
        raise
```

### 2. Input Filter Error Handling
```python
def safe_input_filter(filter_func: HandoffInputFilter) -> HandoffInputFilter:
    """Wrap input filter with error handling."""
    def wrapped_filter(data: HandoffInputData) -> HandoffInputData:
        try:
            result = filter_func(data)
            if not isinstance(result, HandoffInputData):
                raise TypeError("Filter must return HandoffInputData")
            return result
        except Exception as e:
            logger.warning(f"Input filter failed: {e}, using original data")
            return data  # Fallback to original data

    return wrapped_filter
```

### 3. Handoff Execution Error Handling
```python
async def safe_handoff_execution(
    handoff_obj: Handoff,
    ctx: RunContextWrapper,
    input_json: str
) -> Agent:
    """Execute handoff with comprehensive error handling."""
    try:
        # Validate input if schema exists
        if handoff_obj.input_json_schema and input_json:
            # Validate JSON against schema
            json.loads(input_json)  # Basic JSON validation

        # Execute handoff
        result_agent = await handoff_obj.on_invoke_handoff(ctx, input_json)

        if not isinstance(result_agent, Agent):
            raise TypeError("Handoff must return Agent instance")

        return result_agent

    except ModelBehaviorError:
        # Re-raise model behavior errors
        raise
    except Exception as e:
        logger.error(f"Handoff execution failed: {e}")
        raise ModelBehaviorError(f"Handoff failed: {str(e)}")
```

## Advanced Handoff Patterns

### 1. Dynamic Agent Selection
```python
class AgentRouter:
    def __init__(self):
        self.agents = {
            "billing": billing_agent,
            "technical": technical_agent,
            "escalation": escalation_agent
        }

    async def route_request(self, ctx: RunContextWrapper, routing_data: dict) -> Agent:
        agent_type = routing_data.get("type", "escalation")
        return self.agents.get(agent_type, self.agents["escalation"])

router = AgentRouter()

dynamic_handoff = handoff(
    agent=escalation_agent,  # Default fallback
    on_handoff=router.route_request,
    input_type=dict
)
```

### 2. Handoff Chain with State Management
```python
class WorkflowState:
    def __init__(self):
        self.current_step = 0
        self.data = {}

async def workflow_handoff(ctx: RunContextWrapper, step_data: dict) -> Agent:
    state = ctx.context.workflow_state
    state.data.update(step_data)

    workflow_steps = [data_collector, data_processor, report_generator]

    if state.current_step < len(workflow_steps):
        next_agent = workflow_steps[state.current_step]
        state.current_step += 1
        return next_agent
    else:
        return report_generator  # Final step
```

### 3. Conditional Handoff with Fallback
```python
def smart_handoff_enablement(ctx: RunContextWrapper, agent: Agent) -> bool:
    # Check multiple conditions
    conditions = [
        ctx.context.user_authenticated,
        ctx.context.has_permission(f"access_{agent.name.lower()}"),
        agent.name in ctx.context.available_agents
    ]
    return all(conditions)

fallback_handoff = handoff(
    agent=general_agent,
    is_enabled=lambda ctx, agent: True  # Always enabled fallback
)

premium_handoff = handoff(
    agent=premium_agent,
    is_enabled=smart_handoff_enablement
)

# Use both in agent configuration
triage_agent = Agent(
    name="Smart Triage",
    handoffs=[premium_handoff, fallback_handoff]  # Order matters!
)
```

## Performance Considerations

### 1. Input Filter Optimization
- Keep filters lightweight and fast
- Avoid deep copying large conversation histories
- Use lazy evaluation where possible
- Cache filter results when appropriate

### 2. Handoff Callback Efficiency
- Minimize async operations in on_handoff callbacks
- Use background tasks for heavy operations
- Implement timeouts for external service calls
- Consider callback execution order

### 3. Memory Management
- HandoffInputData is frozen - no accidental mutations
- Tuple usage for immutable collections
- Consider conversation history size limits
- Implement cleanup for long-running workflows

## Security Best Practices

### 1. Input Validation
- Always validate handoff input data
- Use strict JSON schemas
- Sanitize user-provided data
- Implement input size limits

### 2. Access Control
- Use is_enabled for permission checks
- Validate agent access rights
- Implement role-based handoff restrictions
- Log all handoff attempts

### 3. Data Privacy
- Use input filters to remove sensitive data
- Implement data retention policies
- Consider encryption for sensitive handoff data
- Audit handoff data flows

### 4. Error Information Disclosure
- Avoid exposing internal errors to LLM
- Use generic error messages for security failures
- Log detailed errors securely
- Implement error rate limiting
```
