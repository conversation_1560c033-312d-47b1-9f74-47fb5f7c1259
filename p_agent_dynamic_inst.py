import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")


@dataclass
class UserContext:
    name: str
    role: str

def dynamic_instructions(
        context: RunContextWrapper[UserContext], agent: Agent[UserContext]
)->str:
    return f"Users Name {context.context.name}, and role is {context.context.role}. Pleas Help Him Plitely."




assistant_agent = Agent[UserContext](
    name="SmartAssistant",
    instructions=dynamic_instructions,
 )

async def main():
    context = UserContext(name="Bila<PERSON>", role="student")
    result  = await Runner.run(
        starting_agent=assistant_agent,
        input="tell me about Ai tools",
        context=context,)
    print("\n--- Final Output ---\n")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())
