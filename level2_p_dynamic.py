import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool, ModelSettings
from agents.agent import StopAtTools
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")




@function_tool
def get_weather_in_tokyo() -> str:
    return f"The weather in Karachi is not sunny today."


@function_tool
def get_weather_in_Karachi() -> str:
    return f"The weather in Karachi is sunny."
agent = Agent(
    name="Haiky agent ",
    instructions="Alwas Respond with haiky format",
    tools=[get_weather_in_tokyo, get_weather_in_Karachi],
    # tool_use_behavior=StopAtTools(stop_at_tool_names=["get_weather_in_Karachi"]),
    model_settings=ModelSettings(parallel_tool_calls=True),
    reset_tool_choice=False,
    # tool_use_behavior=StopAtTools(stop_at_tool_names=["get_weather_in_Karachi"]),


)
result = Runner.run_sync(agent, "what is the weather in Karachi? and tokyo",max_turns=2)
print(result.final_output)



