import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool, ModelSettings
from agents.agent import StopAtTools
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel

load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")
@dataclass
class User:
    name : str
    phon: str
    email: str
    current_user_info: list[str] = None

def get_user_info(name: str) -> User:
    return User(name=name, phon="1234567890", email="<EMAIL>")

def update_user_info(user: User, name: str) -> User:
    user.name = name
    return user
def current_user_info(user: User) -> str:
    return f"Name: {user.name}, Phone: {user.phon}, Email: {user.email}"



async def get_system_prompt(ctx: RunContextWrapper[User], start_agent: Agent[User]):
    ctx.context.name = "<PERSON>"
    ctx.context.current_user_info = current_user_info(ctx.context)
    
    return f"""
You are a helpful assistant. Always greet the user by name.

User's name: {ctx.context.name}
Phone: {ctx.context.phon}
Email: {ctx.context.email}

Respond politely and personally using their name in the greeting.
"""

agent = Agent(
    name="Haiky agent ",
    instructions=get_system_prompt,
)

user1 = User(name="John", phon="1234567890", email="<EMAIL>")
result = Runner.run_sync(agent, "hi",context=user1)
print(result.final_output)



