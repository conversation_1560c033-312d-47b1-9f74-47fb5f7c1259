import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")


@dataclass
class UserContext:
    name: str
    uid: int

@function_tool
def fetch_user_name(wrapper: RunContextWrapper[UserContext])->str:
    return f"the user name is {wrapper.context.name}."

@function_tool
def fetch_user_uid(wrapper: RunContextWrapper[UserContext])->str:
    return f"the user UId is {wrapper.context.name}."

assistant_agent = Agent[UserContext](
    name="SmartAssistant",
    instructions=(
         "You are a helpful assistant. Answer politely and clearly. "
        "You have access to tools to get user's name and UID."
    ),
    tools=[fetch_user_name,fetch_user_uid],
)

async def main():
    context = UserContext(name="<PERSON><PERSON><PERSON>", uid=99999)
    result  = await Runner.run(
        starting_agent=assistant_agent,
        input="Can you Tell me Name and Uid",
        context=context,)
    print("\n--- Final Output ---\n")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())
