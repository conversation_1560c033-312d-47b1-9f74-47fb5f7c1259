# OpenAI Agent SDK Analysis - tool.py

## Table of Contents
1. [Classes Overview](#classes-overview)
2. [Inheritance Structure](#inheritance-structure)
3. [Detailed Class Analysis](#detailed-class-analysis)
4. [Parameter Requirements & Error Analysis](#parameter-requirements--error-analysis)
5. [Type Aliases & Functions](#type-aliases--functions)

## Classes Overview

| Class Name | Type | Purpose | Required Parameters | Optional Parameters |
|------------|------|---------|-------------------|-------------------|
| `FunctionToolResult` | @dataclass | Result container for function tool execution | `tool`, `output`, `run_item` | None |
| `FunctionTool` | @dataclass | Wrapper for Python functions as tools | `name`, `description`, `params_json_schema`, `on_invoke_tool` | `strict_json_schema`, `is_enabled` |
| `FileSearchTool` | @dataclass | Hosted vector store search tool | `vector_store_ids` | `max_num_results`, `include_search_results`, `ranking_options`, `filters` |
| `WebSearchTool` | @dataclass | Hosted web search tool | None | `user_location`, `search_context_size` |
| `ComputerTool` | @dataclass | Hosted computer control tool | `computer` | None |
| `MCPToolApprovalRequest` | @dataclass | MCP tool approval request container | `ctx_wrapper`, `data` | None |
| `MCPToolApprovalFunctionResult` | TypedDict | MCP approval function result | `approve` | `reason` |
| `HostedMCPTool` | @dataclass | Hosted MCP server tool | `tool_config` | `on_approval_request` |
| `CodeInterpreterTool` | @dataclass | Hosted code execution tool | `tool_config` | None |
| `ImageGenerationTool` | @dataclass | Hosted image generation tool | `tool_config` | None |
| `LocalShellCommandRequest` | @dataclass | Local shell command request container | `ctx_wrapper`, `data` | None |
| `LocalShellTool` | @dataclass | Local shell execution tool | `executor` | None |

## Inheritance Structure

```
@dataclass
    ├── FunctionToolResult
    ├── FunctionTool
    ├── FileSearchTool
    ├── WebSearchTool
    ├── ComputerTool
    ├── MCPToolApprovalRequest
    ├── HostedMCPTool
    ├── CodeInterpreterTool
    ├── ImageGenerationTool
    ├── LocalShellCommandRequest
    └── LocalShellTool

TypedDict
    └── MCPToolApprovalFunctionResult

Union Type
    └── Tool (Union of all tool types)
```

## Detailed Class Analysis

### 1. FunctionToolResult
**Type**: `@dataclass`
**Purpose**: Container for function tool execution results

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `tool` | `FunctionTool` | ✅ Yes | None | The tool that was executed |
| `output` | `Any` | ✅ Yes | None | The output of the tool |
| `run_item` | `RunItem` | ✅ Yes | None | The run item produced by tool call |

**Potential Errors**:
- `TypeError`: If any required parameter is missing or wrong type

### 2. FunctionTool
**Type**: `@dataclass`
**Purpose**: Wrapper for Python functions to be used as agent tools

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `name` | `str` | ✅ Yes | None | Tool name shown to LLM |
| `description` | `str` | ✅ Yes | None | Tool description for LLM |
| `params_json_schema` | `dict[str, Any]` | ✅ Yes | None | JSON schema for parameters |
| `on_invoke_tool` | `Callable[[ToolContext[Any], str], Awaitable[Any]]` | ✅ Yes | None | Tool invocation function |
| `strict_json_schema` | `bool` | ❌ No | `True` | Enable strict JSON schema mode |
| `is_enabled` | `bool \| Callable` | ❌ No | `True` | Tool enabled state or function |

**Potential Errors**:
- `TypeError`: Missing required parameters
- `ModelBehaviorError`: Invalid JSON input during execution
- `ValidationError`: Parameter validation failures

### 3. FileSearchTool
**Type**: `@dataclass`
**Purpose**: Hosted tool for vector store search (OpenAI only)

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `vector_store_ids` | `list[str]` | ✅ Yes | None | Vector store IDs to search |
| `max_num_results` | `int \| None` | ❌ No | `None` | Maximum search results |
| `include_search_results` | `bool` | ❌ No | `False` | Include results in LLM output |
| `ranking_options` | `RankingOptions \| None` | ❌ No | `None` | Search ranking options |
| `filters` | `Filters \| None` | ❌ No | `None` | File attribute filters |

**Potential Errors**:
- `TypeError`: If `vector_store_ids` is not a list of strings
- `ValueError`: If vector store IDs are invalid or inaccessible

### 4. WebSearchTool
**Type**: `@dataclass`
**Purpose**: Hosted tool for web search (OpenAI only)

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `user_location` | `UserLocation \| None` | ❌ No | `None` | Location for search customization |
| `search_context_size` | `Literal["low", "medium", "high"]` | ❌ No | `"medium"` | Amount of search context |

**Potential Errors**:
- `ValueError`: If `search_context_size` is not one of the allowed literals
- `TypeError`: If `user_location` is not valid UserLocation type

### 5. ComputerTool
**Type**: `@dataclass`
**Purpose**: Hosted tool for computer control

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `computer` | `Computer \| AsyncComputer` | ✅ Yes | None | Computer implementation |

**Potential Errors**:
- `TypeError`: If `computer` is not Computer or AsyncComputer instance
- `RuntimeError`: Computer implementation errors during execution

### 6. HostedMCPTool
**Type**: `@dataclass`
**Purpose**: Tool for remote MCP server integration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `tool_config` | `Mcp` | ✅ Yes | None | MCP tool configuration |
| `on_approval_request` | `MCPToolApprovalFunction \| None` | ❌ No | `None` | Approval request handler |

**Potential Errors**:
- `TypeError`: If `tool_config` is not valid Mcp configuration
- `ConnectionError`: MCP server connection issues
- `TimeoutError`: MCP server response timeouts

### 7. CodeInterpreterTool
**Type**: `@dataclass`
**Purpose**: Hosted tool for code execution in sandboxed environment

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `tool_config` | `CodeInterpreter` | ✅ Yes | None | Code interpreter configuration |

**Potential Errors**:
- `TypeError`: If `tool_config` is not valid CodeInterpreter configuration
- `SecurityError`: Sandbox security violations
- `ExecutionError`: Code execution failures

### 8. ImageGenerationTool
**Type**: `@dataclass`
**Purpose**: Hosted tool for image generation

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `tool_config` | `ImageGeneration` | ✅ Yes | None | Image generation configuration |

**Potential Errors**:
- `TypeError`: If `tool_config` is not valid ImageGeneration configuration
- `QuotaExceededError`: Image generation quota limits
- `ContentPolicyError`: Generated content policy violations

### 9. LocalShellTool
**Type**: `@dataclass`
**Purpose**: Tool for local shell command execution

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `executor` | `LocalShellExecutor` | ✅ Yes | None | Shell command executor function |

**Potential Errors**:
- `TypeError`: If `executor` is not callable
- `SecurityError`: Shell command security violations
- `ExecutionError`: Command execution failures
- `PermissionError`: Insufficient permissions for command

## Type Aliases & Functions

### Type Aliases:
| Name | Type | Purpose |
|------|------|---------|
| `Tool` | Union of all tool types | Generic tool type |
| `ToolFunction` | Union of function signatures | Function tool signatures |
| `ToolErrorFunction` | Callable for error handling | Tool error message generation |
| `MCPToolApprovalFunction` | Callable for MCP approval | MCP tool approval logic |
| `LocalShellExecutor` | Callable for shell execution | Shell command execution |

### Key Functions:
| Function | Purpose | Parameters | Return Type |
|----------|---------|------------|-------------|
| `function_tool()` | Create FunctionTool from function | Function + options | `FunctionTool` |
| `default_tool_error_function()` | Default error message generator | `ctx`, `error` | `str` |

## Parameter Requirements & Error Analysis

### Critical Required Parameters by Tool Type:
1. **FunctionTool**: `name`, `description`, `params_json_schema`, `on_invoke_tool`
2. **FileSearchTool**: `vector_store_ids`
3. **ComputerTool**: `computer`
4. **HostedMCPTool**: `tool_config`
5. **CodeInterpreterTool**: `tool_config`
6. **ImageGenerationTool**: `tool_config`
7. **LocalShellTool**: `executor`

### Common Error Scenarios:

#### Function Tool Errors:
1. **Missing required parameters**: `TypeError` for name, description, schema, or invoke function
2. **Invalid JSON input**: `ModelBehaviorError` when LLM provides malformed JSON
3. **Parameter validation**: `ValidationError` when parameters don't match schema
4. **Function execution**: Various errors depending on wrapped function

#### Hosted Tool Errors:
1. **Configuration errors**: `TypeError` for invalid tool configurations
2. **Service availability**: `ConnectionError` for unreachable hosted services
3. **Quota limits**: `QuotaExceededError` for service usage limits
4. **Content policy**: `ContentPolicyError` for policy violations

#### Security Errors:
1. **Shell execution**: `SecurityError` for dangerous commands
2. **File access**: `PermissionError` for unauthorized file operations
3. **Network access**: `ConnectionError` for blocked network operations

## Excel-Style Parameter Reference Table

| Class | Parameter | Type | Required | Default Value | Validation Rules | Error if Missing/Invalid |
|-------|-----------|------|----------|---------------|------------------|-------------------------|
| FunctionToolResult | tool | FunctionTool | ✅ | None | Valid FunctionTool instance | TypeError: missing required argument |
| FunctionToolResult | output | Any | ✅ | None | Any value | TypeError: missing required argument |
| FunctionToolResult | run_item | RunItem | ✅ | None | Valid RunItem instance | TypeError: missing required argument |
| FunctionTool | name | str | ✅ | None | Non-empty string | TypeError: missing required argument |
| FunctionTool | description | str | ✅ | None | String description | TypeError: missing required argument |
| FunctionTool | params_json_schema | dict[str,Any] | ✅ | None | Valid JSON schema dict | TypeError: missing required argument |
| FunctionTool | on_invoke_tool | Callable | ✅ | None | Async callable with correct signature | TypeError: missing required argument |
| FunctionTool | strict_json_schema | bool | ❌ | True | Boolean value | TypeError on non-boolean |
| FunctionTool | is_enabled | bool\|Callable | ❌ | True | Boolean or callable returning bool | TypeError on invalid type |
| FileSearchTool | vector_store_ids | list[str] | ✅ | None | List of valid vector store IDs | TypeError: missing required argument |
| FileSearchTool | max_num_results | int\|None | ❌ | None | Positive integer or None | ValueError on negative integer |
| FileSearchTool | include_search_results | bool | ❌ | False | Boolean value | TypeError on non-boolean |
| FileSearchTool | ranking_options | RankingOptions\|None | ❌ | None | Valid RankingOptions or None | TypeError on invalid options |
| FileSearchTool | filters | Filters\|None | ❌ | None | Valid Filters or None | TypeError on invalid filters |
| WebSearchTool | user_location | UserLocation\|None | ❌ | None | Valid UserLocation or None | TypeError on invalid location |
| WebSearchTool | search_context_size | Literal | ❌ | "medium" | "low", "medium", or "high" | ValueError on invalid literal |
| ComputerTool | computer | Computer\|AsyncComputer | ✅ | None | Valid Computer implementation | TypeError: missing required argument |
| MCPToolApprovalRequest | ctx_wrapper | RunContextWrapper | ✅ | None | Valid context wrapper | TypeError: missing required argument |
| MCPToolApprovalRequest | data | McpApprovalRequest | ✅ | None | Valid approval request data | TypeError: missing required argument |
| MCPToolApprovalFunctionResult | approve | bool | ✅ | None | Boolean approval decision | KeyError: missing required key |
| MCPToolApprovalFunctionResult | reason | str | ❌ | None | String reason for rejection | TypeError on non-string |
| HostedMCPTool | tool_config | Mcp | ✅ | None | Valid MCP configuration | TypeError: missing required argument |
| HostedMCPTool | on_approval_request | MCPToolApprovalFunction\|None | ❌ | None | Valid approval function or None | TypeError on invalid function |
| CodeInterpreterTool | tool_config | CodeInterpreter | ✅ | None | Valid CodeInterpreter config | TypeError: missing required argument |
| ImageGenerationTool | tool_config | ImageGeneration | ✅ | None | Valid ImageGeneration config | TypeError: missing required argument |
| LocalShellCommandRequest | ctx_wrapper | RunContextWrapper | ✅ | None | Valid context wrapper | TypeError: missing required argument |
| LocalShellCommandRequest | data | LocalShellCall | ✅ | None | Valid shell call data | TypeError: missing required argument |
| LocalShellTool | executor | LocalShellExecutor | ✅ | None | Valid executor function | TypeError: missing required argument |

## Tool Type Comparison Table

| Tool Type | Hosting | Purpose | OpenAI Only | Security Level | Complexity |
|-----------|---------|---------|-------------|----------------|------------|
| FunctionTool | Local | Custom Python functions | ❌ No | Medium | Low |
| FileSearchTool | Hosted | Vector store search | ✅ Yes | Low | Low |
| WebSearchTool | Hosted | Web search | ✅ Yes | Low | Low |
| ComputerTool | Hosted | Computer control | ❌ No | High | High |
| HostedMCPTool | Hosted | Remote MCP servers | ✅ Yes | Medium | Medium |
| CodeInterpreterTool | Hosted | Code execution | ✅ Yes | High | Medium |
| ImageGenerationTool | Hosted | Image generation | ✅ Yes | Low | Low |
| LocalShellTool | Local | Shell commands | ❌ No | Very High | Medium |

## Object Relationship Mapping

| Parent Object | Child/Related Objects | Relationship Type | Usage Context |
|---------------|----------------------|-------------------|---------------|
| FunctionTool | ToolContext | Usage | Function execution context |
| FunctionTool | RunContextWrapper | Usage | Agent run context |
| FunctionTool | JSON Schema | Composition | Parameter validation |
| FileSearchTool | Vector Store | External | Search data source |
| WebSearchTool | Search Engine | External | Web search service |
| ComputerTool | Computer/AsyncComputer | Composition | Computer interface |
| HostedMCPTool | MCP Server | External | Remote tool provider |
| CodeInterpreterTool | Sandbox Environment | External | Code execution environment |
| ImageGenerationTool | Image Service | External | Image generation service |
| LocalShellTool | Shell Executor | Composition | Command execution interface |
| MCPToolApprovalRequest | Approval Function | Usage | Tool approval workflow |
| LocalShellCommandRequest | Shell Executor | Usage | Command execution workflow |

## Function Tool Creation Patterns

### 1. Basic Function Tool
```python
@function_tool
def my_tool(param1: str, param2: int) -> str:
    """Tool description here.

    Args:
        param1: Description of param1
        param2: Description of param2
    """
    return f"Result: {param1} - {param2}"
```

### 2. Function Tool with Context
```python
@function_tool
def context_tool(ctx: RunContextWrapper[MyContext], data: str) -> str:
    """Tool that uses context.

    Args:
        data: Input data to process
    """
    user_id = ctx.context.user_id
    return f"Processed {data} for user {user_id}"
```

### 3. Function Tool with Custom Configuration
```python
@function_tool(
    name_override="custom_name",
    description_override="Custom description",
    strict_mode=True,
    is_enabled=lambda ctx, agent: ctx.context.has_permission("tool_use")
)
def configured_tool(value: float) -> float:
    return value * 2
```

## Common Usage Patterns & Error Prevention

### 1. Function Tool Best Practices
```python
# ✅ Good: Clear types and docstring
@function_tool
def calculate_area(length: float, width: float) -> float:
    """Calculate rectangle area.

    Args:
        length: Rectangle length in meters
        width: Rectangle width in meters

    Returns:
        Area in square meters
    """
    if length <= 0 or width <= 0:
        raise ValueError("Dimensions must be positive")
    return length * width

# ❌ Bad: No types, unclear purpose
@function_tool
def calc(a, b):
    return a * b
```

### 2. Hosted Tool Configuration
```python
# ✅ Good: Proper vector store setup
file_search = FileSearchTool(
    vector_store_ids=["vs_123", "vs_456"],
    max_num_results=10,
    include_search_results=True
)

# ❌ Bad: Empty vector store list
file_search = FileSearchTool(
    vector_store_ids=[],  # Will cause errors
    max_num_results=-1    # Invalid negative value
)
```

### 3. MCP Tool with Approval
```python
# ✅ Good: Proper approval handling
async def approve_mcp_tool(request: MCPToolApprovalRequest) -> MCPToolApprovalFunctionResult:
    # Check if tool is safe to execute
    if request.data.tool_name in SAFE_TOOLS:
        return {"approve": True}
    else:
        return {"approve": False, "reason": "Tool not in safe list"}

mcp_tool = HostedMCPTool(
    tool_config=mcp_config,
    on_approval_request=approve_mcp_tool
)
```

### 4. Local Shell Tool Security
```python
# ✅ Good: Safe command execution
async def safe_shell_executor(request: LocalShellCommandRequest) -> str:
    command = request.data.command

    # Validate command safety
    if not is_safe_command(command):
        return "Error: Command not allowed for security reasons"

    # Execute with timeout and limits
    result = await execute_with_limits(command, timeout=30)
    return result

shell_tool = LocalShellTool(executor=safe_shell_executor)

# ❌ Bad: Unsafe direct execution
async def unsafe_executor(request: LocalShellCommandRequest) -> str:
    # Direct execution without validation - DANGEROUS!
    return os.system(request.data.command)
```

## Error Handling Strategies

### 1. Function Tool Error Handling
```python
def custom_error_handler(ctx: RunContextWrapper, error: Exception) -> str:
    if isinstance(error, ValueError):
        return f"Invalid input: {str(error)}"
    elif isinstance(error, PermissionError):
        return "Access denied: insufficient permissions"
    else:
        return f"Unexpected error: {str(error)}"

@function_tool(failure_error_function=custom_error_handler)
def risky_tool(value: str) -> str:
    # Tool implementation that might fail
    pass
```

### 2. Tool Enablement Logic
```python
def tool_enabled_check(ctx: RunContextWrapper[MyContext], agent: Agent) -> bool:
    # Dynamic enablement based on context
    return (
        ctx.context.user_tier == "premium" and
        ctx.context.has_permission("advanced_tools")
    )

@function_tool(is_enabled=tool_enabled_check)
def premium_tool(data: str) -> str:
    return f"Premium processing: {data}"
```

### 3. Validation and Type Safety
```python
from pydantic import BaseModel, validator

class ToolParams(BaseModel):
    email: str
    age: int

    @validator('email')
    def validate_email(cls, v):
        if '@' not in v:
            raise ValueError('Invalid email format')
        return v

    @validator('age')
    def validate_age(cls, v):
        if v < 0 or v > 150:
            raise ValueError('Age must be between 0 and 150')
        return v

@function_tool
def validated_tool(params: ToolParams) -> str:
    return f"Processing for {params.email}, age {params.age}"
```

## Security Considerations

### 1. Function Tool Security
- Validate all inputs thoroughly
- Use type hints and Pydantic models for validation
- Implement proper error handling
- Avoid exposing sensitive information in error messages
- Use `is_enabled` for access control

### 2. Shell Tool Security
- Whitelist allowed commands
- Sanitize command inputs
- Use timeouts and resource limits
- Run in sandboxed environments
- Log all command executions

### 3. MCP Tool Security
- Implement approval workflows for sensitive operations
- Validate MCP server certificates
- Use secure communication channels
- Monitor tool usage patterns
- Implement rate limiting

### 4. Hosted Tool Security
- Understand data privacy implications
- Configure appropriate filters and limits
- Monitor usage quotas
- Review generated content for policy compliance
