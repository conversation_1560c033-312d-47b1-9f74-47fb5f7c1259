import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool, handoff
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")


# billing_agent = Agent(name="Billing agent")
# external_refund_agent = Agent(name="Refund agent")
refund_agent = Agent(name="Refund agent")

general_agent = Agent(name="General agent",handoffs=[
    handoff(agent=refund_agent,
            tool_name_override="refund",
            tool_description_override="handles a rfund request",
            is_enabled=False
            )
])

async def main():
    result  = await Runner.run(
        general_agent,"I want to refund my order")
    print("\n--- Final Output ---\n")
    print(result.final_output)
    print(result.last_agent.name)

if __name__ == "__main__":
    asyncio.run(main())
