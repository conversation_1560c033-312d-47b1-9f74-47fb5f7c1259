import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner
import asyncio
from pydantic import BaseModel
import json
import pprint
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")


class HomeworkOutput(BaseModel):
    is_homework: bool
    reasoning: str

guardrail_agent = Agent(
    name="Guardrail Check",
    instructions="Check if The User is asking abourt homework",
    output_type=HomeworkOutput,
)


math_totor_agent = Agent(
    name="Math Tutor",
    handoff_description="Specialist agent for math questions",
    instructions="You provide help with math problems. Explain your reasoning at each step and include examples.")

history_tutor_agent = Agent(
    name="History Tutor",
    handoff_description="Specialist agent for historical questions",
    instructions="You provide assistance with historical queries. Explain important events and context clearly.")

async def homework_guardrail(ctx, agent, input_data):
    print(f"\n[DEBUG] input_data received in guardrail: {input_data}\n")  # <-- Add this line
    pprint.pprint(input_data)
    pprint.pprint(ctx)
    result = await Runner.run(guardrail_agent, input_data, context=ctx.context)
    final_output = result.final_output_as(HomeworkOutput)
    
    print(f"[DEBUG] Guardrail Output: {final_output}\n")  # Optional: show output structure

    return GuardrailFunctionOutput(
        output_info=final_output,
        tripwire_triggered=not final_output.is_homework,
    )

triage_agent = Agent(
    name="Triage Agent",
    instructions="You determine which agent to use based on the user's homework",
    input_guardrails=[
        InputGuardrail(guardrail_function=homework_guardrail),
    ],
    handoffs=[math_totor_agent, history_tutor_agent],
)

async def main():
    result = await Runner.run(triage_agent, "how can i solve this math problem")
    print(result.final_output)

    result = await Runner.run(triage_agent, "what is life")
    print(result.final_output)
    print(json.dumps(result.model_dump(), indent=2))
if __name__ == "__main__":
    asyncio.run(main())

# Code within the code,
# Functions calling themselves,
# Infinite loop's dance.