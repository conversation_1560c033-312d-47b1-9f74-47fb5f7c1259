# OpenAI Agent SDK Analysis - agent.py

## Table of Contents
1. [Classes Overview](#classes-overview)
2. [Inheritance Structure](#inheritance-structure)
3. [Detailed Class Analysis](#detailed-class-analysis)
4. [Parameter Requirements & Error Analysis](#parameter-requirements--error-analysis)
5. [Type Aliases & Functions](#type-aliases--functions)

## Classes Overview

| Class Name | Type | Purpose | Required Parameters | Optional Parameters |
|------------|------|---------|-------------------|-------------------|
| `ToolsToFinalOutputResult` | @dataclass | Result container for tool-to-output conversion | `is_final_output: bool` | `final_output: Any \| None` |
| `StopAtTools` | TypedDict | Configuration for stopping at specific tools | `stop_at_tool_names: list[str]` | None |
| `MCPConfig` | TypedDict | MCP server configuration | None | `convert_schemas_to_strict: bool` |
| `Agent[TContext]` | @dataclass, Generic | Main agent class with AI model configuration | `name: str` | All others (see detailed analysis) |

## Inheritance Structure

```
Generic[TContext]
    └── Agent[TContext]

TypedDict
    ├── StopAtTools
    └── MCPConfig

@dataclass
    ├── ToolsToFinalOutputResult
    └── Agent[TContext]
```

## Detailed Class Analysis

### 1. ToolsToFinalOutputResult
**Type**: `@dataclass`
**Purpose**: Container for tool execution results that determine if output is final

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `is_final_output` | `bool` | ✅ Yes | None | Whether this is the final output |
| `final_output` | `Any \| None` | ❌ No | `None` | The final output value |

**Potential Errors**:
- `TypeError`: If `is_final_output` is not provided or not boolean
- `ValidationError`: If `final_output` doesn't match agent's `output_type` when `is_final_output=True`

### 2. StopAtTools
**Type**: `TypedDict`
**Purpose**: Configuration for stopping agent execution at specific tools

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `stop_at_tool_names` | `list[str]` | ✅ Yes | None | List of tool names that stop execution |

**Potential Errors**:
- `TypeError`: If `stop_at_tool_names` is not a list
- `ValueError`: If list contains non-string elements
- `KeyError`: If required key is missing from dict

### 3. MCPConfig
**Type**: `TypedDict`
**Purpose**: Configuration for Model Context Protocol servers

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `convert_schemas_to_strict` | `bool` | ❌ No | `False` | Convert MCP schemas to strict mode |

**Potential Errors**:
- `TypeError`: If `convert_schemas_to_strict` is not boolean when provided

### 4. Agent[TContext]
**Type**: `@dataclass`, `Generic[TContext]`
**Purpose**: Main AI agent class with comprehensive configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `name` | `str` | ✅ Yes | None | Agent identifier |
| `instructions` | `str \| Callable \| None` | ❌ No | `None` | System prompt/instructions |
| `prompt` | `Prompt \| DynamicPromptFunction \| None` | ❌ No | `None` | Dynamic prompt configuration |
| `handoff_description` | `str \| None` | ❌ No | `None` | Description for handoff usage |
| `handoffs` | `list[Agent[Any] \| Handoff[TContext]]` | ❌ No | `[]` | Sub-agents for delegation |
| `model` | `str \| Model \| None` | ❌ No | `None` | Model implementation |
| `model_settings` | `ModelSettings` | ❌ No | `ModelSettings()` | Model tuning parameters |
| `tools` | `list[Tool]` | ❌ No | `[]` | Available tools |
| `mcp_servers` | `list[MCPServer]` | ❌ No | `[]` | MCP servers |
| `mcp_config` | `MCPConfig` | ❌ No | `MCPConfig()` | MCP configuration |
| `input_guardrails` | `list[InputGuardrail[TContext]]` | ❌ No | `[]` | Input validation checks |
| `output_guardrails` | `list[OutputGuardrail[TContext]]` | ❌ No | `[]` | Output validation checks |
| `output_type` | `type[Any] \| AgentOutputSchemaBase \| None` | ❌ No | `None` | Output type specification |
| `hooks` | `AgentHooks[TContext] \| None` | ❌ No | `None` | Lifecycle event callbacks |
| `tool_use_behavior` | `Literal \| StopAtTools \| ToolsToFinalOutputFunction` | ❌ No | `"run_llm_again"` | Tool usage behavior |
| `reset_tool_choice` | `bool` | ❌ No | `True` | Reset tool choice after use |

## Parameter Requirements & Error Analysis

### Critical Required Parameters
1. **Agent.name**: Must be provided, `TypeError` if missing

### Common Error Scenarios

#### Agent Class Errors:
1. **Missing name**: `TypeError: __init__() missing 1 required positional argument: 'name'`
2. **Invalid instructions type**: Runtime error if not `str`, `Callable`, or `None`
3. **Invalid model type**: `TypeError` if not `str`, `Model`, or `None`
4. **Invalid tool_use_behavior**: `TypeError` if not one of the allowed literal types
5. **MCP server lifecycle**: `RuntimeError` if MCP servers not properly connected/cleaned up
6. **Output type mismatch**: `ValidationError` if final output doesn't match specified `output_type`

#### TypedDict Errors:
1. **StopAtTools**: `KeyError` if `stop_at_tool_names` missing, `TypeError` if not list of strings
2. **MCPConfig**: `TypeError` if `convert_schemas_to_strict` not boolean

### Best Practices for Error Prevention:
1. Always provide `name` parameter for Agent
2. Ensure MCP servers are properly connected before use
3. Match output types with agent's `output_type` specification
4. Validate tool names exist before using in `StopAtTools`
5. Use proper type hints to catch type mismatches early

## Type Aliases & Functions

| Name | Type | Purpose |
|------|------|---------|
| `ToolsToFinalOutputFunction` | TypeAlias | Function type for custom tool-to-output conversion |

### Key Methods in Agent Class:
- `clone(**kwargs)`: Create agent copy with modified parameters
- `as_tool()`: Convert agent to tool for use by other agents
- `get_system_prompt()`: Retrieve system prompt (async)
- `get_prompt()`: Get prompt configuration (async)
- `get_mcp_tools()`: Fetch MCP server tools (async)
- `get_all_tools()`: Get all available tools (async)

## Excel-Style Parameter Reference Table

| Class | Parameter | Type | Required | Default Value | Validation Rules | Error if Missing/Invalid |
|-------|-----------|------|----------|---------------|------------------|-------------------------|
| Agent | name | str | ✅ | None | Non-empty string | TypeError: missing required argument |
| Agent | instructions | str\|Callable\|None | ❌ | None | Must be string or callable | Runtime error on invalid type |
| Agent | prompt | Prompt\|DynamicPromptFunction\|None | ❌ | None | Valid Prompt object | TypeError on invalid type |
| Agent | handoff_description | str\|None | ❌ | None | String or None | TypeError on invalid type |
| Agent | handoffs | list[Agent\|Handoff] | ❌ | [] | List of valid agents/handoffs | TypeError on invalid list items |
| Agent | model | str\|Model\|None | ❌ | None | Valid model string or Model object | TypeError/ValueError on invalid model |
| Agent | model_settings | ModelSettings | ❌ | ModelSettings() | Valid ModelSettings object | TypeError on invalid object |
| Agent | tools | list[Tool] | ❌ | [] | List of Tool objects | TypeError on invalid tool objects |
| Agent | mcp_servers | list[MCPServer] | ❌ | [] | List of connected MCPServer objects | RuntimeError if not connected |
| Agent | mcp_config | MCPConfig | ❌ | MCPConfig() | Valid MCPConfig dict | TypeError on invalid config |
| Agent | input_guardrails | list[InputGuardrail] | ❌ | [] | List of InputGuardrail objects | TypeError on invalid guardrail |
| Agent | output_guardrails | list[OutputGuardrail] | ❌ | [] | List of OutputGuardrail objects | TypeError on invalid guardrail |
| Agent | output_type | type\|AgentOutputSchemaBase\|None | ❌ | None | Valid type or schema | ValidationError on type mismatch |
| Agent | hooks | AgentHooks\|None | ❌ | None | Valid AgentHooks object | TypeError on invalid hooks |
| Agent | tool_use_behavior | Literal\|StopAtTools\|Function | ❌ | "run_llm_again" | Valid behavior type | TypeError on invalid behavior |
| Agent | reset_tool_choice | bool | ❌ | True | Boolean value | TypeError on non-boolean |
| ToolsToFinalOutputResult | is_final_output | bool | ✅ | None | Boolean value | TypeError: missing required argument |
| ToolsToFinalOutputResult | final_output | Any\|None | ❌ | None | Any value or None | No error (flexible type) |
| StopAtTools | stop_at_tool_names | list[str] | ✅ | None | List of string tool names | KeyError: missing required key |
| MCPConfig | convert_schemas_to_strict | bool | ❌ | False | Boolean value | TypeError on non-boolean |

## Object Relationship Mapping

| Parent Object | Child/Related Objects | Relationship Type | Usage Context |
|---------------|----------------------|-------------------|---------------|
| Agent | ModelSettings | Composition | Model configuration |
| Agent | Tool | Aggregation | Available tools list |
| Agent | MCPServer | Aggregation | External tool providers |
| Agent | InputGuardrail | Aggregation | Input validation |
| Agent | OutputGuardrail | Aggregation | Output validation |
| Agent | AgentHooks | Association | Lifecycle callbacks |
| Agent | Handoff | Aggregation | Sub-agent delegation |
| Agent | Prompt | Association | Dynamic prompting |
| ToolsToFinalOutputResult | Agent | Usage | Tool execution results |
| StopAtTools | Agent | Configuration | Tool behavior control |
| MCPConfig | Agent | Configuration | MCP server settings |

## Common Usage Patterns & Error Prevention

### 1. Basic Agent Creation
```python
# Minimal required setup
agent = Agent(name="my_agent")  # ✅ Valid

# Common error - missing name
agent = Agent()  # ❌ TypeError: missing required argument 'name'
```

### 2. Tool Configuration
```python
# Correct tool setup
agent = Agent(
    name="tool_agent",
    tools=[some_tool, another_tool],  # ✅ List of Tool objects
    tool_use_behavior="stop_on_first_tool"  # ✅ Valid literal
)

# Common errors
agent = Agent(
    name="bad_agent",
    tools="not_a_list",  # ❌ TypeError: expected list
    tool_use_behavior="invalid_behavior"  # ❌ TypeError: invalid literal
)
```

### 3. MCP Server Integration
```python
# Correct MCP setup
server = MCPServer(...)
await server.connect()  # ✅ Must connect first
agent = Agent(
    name="mcp_agent",
    mcp_servers=[server],
    mcp_config={"convert_schemas_to_strict": True}
)

# Common error - not connecting server
agent = Agent(
    name="bad_mcp_agent",
    mcp_servers=[unconnected_server]  # ❌ RuntimeError during execution
)
```

## Error Handling Strategies

### 1. Parameter Validation
- Always validate `name` parameter is non-empty string
- Check tool objects are properly instantiated before adding to `tools` list
- Ensure MCP servers are connected before agent execution
- Validate output types match expected schema

### 2. Runtime Error Prevention
- Use try-catch blocks around agent execution
- Implement proper MCP server lifecycle management
- Validate tool results match expected output types
- Handle async operations properly in custom functions

### 3. Type Safety
- Use type hints consistently
- Validate callable parameters return expected types
- Check generic type parameters match context requirements
- Ensure TypedDict structures have required keys
