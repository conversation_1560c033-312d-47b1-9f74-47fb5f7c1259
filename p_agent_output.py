import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")


class CalendarEvent(BaseModel):
    name: str
    date: str
    participants:list[str]

agent = Agent(
    name="Calendar Extractor",
    instructions="Extract Calendear Events from Text",
    output_type=CalendarEvent,
)
result = Runner.run_sync(agent, "We have a team meeting on 5th July with <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.")
print(result.final_output)



