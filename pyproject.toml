[project]
name = "quiz-practice"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "92Bilal26", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "openai-agents>=0.0.19",
    "python-dotenv>=1.1.1",
]

[project.scripts]
quiz-practice = "quiz_practice:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
