# OpenAI Agent SDK Analysis - run.py

## Table of Contents
1. [Classes Overview](#classes-overview)
2. [Inheritance Structure](#inheritance-structure)
3. [Detailed Class Analysis](#detailed-class-analysis)
4. [Parameter Requirements & Error Analysis](#parameter-requirements--error-analysis)
5. [Constants & Functions](#constants--functions)

## Classes Overview

| Class Name | Type | Purpose | Required Parameters | Optional Parameters |
|------------|------|---------|-------------------|-------------------|
| `RunConfig` | @dataclass | Configuration for entire agent run | None | All parameters optional |
| `RunOptions[TContext]` | TypedDict, Generic | Arguments for AgentRunner methods | None | All parameters optional |
| `Runner` | Class | Static methods for running agents | `starting_agent`, `input` | All others optional |
| `AgentRunner` | Class | Core agent execution engine (experimental) | None | All parameters via methods |

## Inheritance Structure

```
Generic[TContext]
    └── RunOptions[TContext]

TypedDict
    └── RunOptions[TContext]

@dataclass
    └── RunConfig

Class (Static Methods)
    └── Runner

Class (Experimental)
    └── AgentRunner
```

## Detailed Class Analysis

### 1. RunConfig
**Type**: `@dataclass`
**Purpose**: Global configuration settings for entire agent run execution

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `model` | `str \| Model \| None` | ❌ No | `None` | Override model for entire run |
| `model_provider` | `ModelProvider` | ❌ No | `MultiProvider()` | Model provider for string model names |
| `model_settings` | `ModelSettings \| None` | ❌ No | `None` | Global model settings override |
| `handoff_input_filter` | `HandoffInputFilter \| None` | ❌ No | `None` | Global input filter for handoffs |
| `input_guardrails` | `list[InputGuardrail[Any]] \| None` | ❌ No | `None` | Input validation checks |
| `output_guardrails` | `list[OutputGuardrail[Any]] \| None` | ❌ No | `None` | Output validation checks |
| `tracing_disabled` | `bool` | ❌ No | `False` | Disable tracing for run |
| `trace_include_sensitive_data` | `bool` | ❌ No | `True` | Include sensitive data in traces |
| `workflow_name` | `str` | ❌ No | `"Agent workflow"` | Logical name for tracing |
| `trace_id` | `str \| None` | ❌ No | `None` | Custom trace ID |
| `group_id` | `str \| None` | ❌ No | `None` | Grouping identifier for traces |
| `trace_metadata` | `dict[str, Any] \| None` | ❌ No | `None` | Additional trace metadata |

**Potential Errors**:
- `TypeError`: If `model_provider` is not a valid ModelProvider instance
- `ValueError`: If `workflow_name` is empty string
- `TypeError`: If guardrails lists contain invalid guardrail objects

### 2. RunOptions[TContext]
**Type**: `TypedDict`, `Generic[TContext]`
**Purpose**: Type-safe arguments container for AgentRunner methods

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `context` | `TContext \| None` | ❌ No | `None` | Run context object |
| `max_turns` | `int` | ❌ No | `DEFAULT_MAX_TURNS` | Maximum execution turns |
| `hooks` | `RunHooks[TContext] \| None` | ❌ No | `None` | Lifecycle event hooks |
| `run_config` | `RunConfig \| None` | ❌ No | `None` | Run configuration |
| `previous_response_id` | `str \| None` | ❌ No | `None` | Previous response ID for continuity |

**Potential Errors**:
- `TypeError`: If `max_turns` is not integer or is negative
- `TypeError`: If `hooks` is not RunHooks instance
- `TypeError`: If `run_config` is not RunConfig instance

### 3. Runner
**Type**: `Class` (Static Methods Only)
**Purpose**: High-level interface for agent execution with three execution modes

#### Key Methods:
| Method | Purpose | Return Type | Async |
|--------|---------|-------------|-------|
| `run()` | Execute agent asynchronously | `RunResult` | ✅ Yes |
| `run_sync()` | Execute agent synchronously | `RunResult` | ❌ No |
| `run_streamed()` | Execute agent with streaming | `RunResultStreaming` | ❌ No |

**Common Parameters for All Methods**:
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `starting_agent` | `Agent[TContext]` | ✅ Yes | None | Initial agent to execute |
| `input` | `str \| list[TResponseInputItem]` | ✅ Yes | None | Input to agent |
| `context` | `TContext \| None` | ❌ No | `None` | Execution context |
| `max_turns` | `int` | ❌ No | `DEFAULT_MAX_TURNS` | Maximum turns (10) |
| `hooks` | `RunHooks[TContext] \| None` | ❌ No | `None` | Lifecycle hooks |
| `run_config` | `RunConfig \| None` | ❌ No | `None` | Run configuration |
| `previous_response_id` | `str \| None` | ❌ No | `None` | Previous response ID |

**Potential Errors**:
- `TypeError`: Missing required `starting_agent` or `input`
- `MaxTurnsExceeded`: When execution exceeds `max_turns`
- `InputGuardrailTripwireTriggered`: When input guardrail fails
- `OutputGuardrailTripwireTriggered`: When output guardrail fails
- `ModelBehaviorError`: When model doesn't produce expected response
- `AgentsException`: General agent execution errors

### 4. AgentRunner
**Type**: `Class` (Experimental - Not Public API)
**Purpose**: Core agent execution engine with detailed control

**⚠️ WARNING**: This class is experimental and should not be used directly or subclassed.

#### Key Methods:
| Method | Purpose | Parameters | Return Type |
|--------|---------|------------|-------------|
| `run()` | Core async execution | `starting_agent`, `input`, `**kwargs` | `RunResult` |
| `run_sync()` | Synchronous wrapper | Same as `run()` | `RunResult` |
| `run_streamed()` | Streaming execution | Same as `run()` | `RunResultStreaming` |

## Parameter Requirements & Error Analysis

### Critical Required Parameters
1. **Runner methods**: `starting_agent` and `input` are mandatory
2. **AgentRunner methods**: Same requirements as Runner

### Common Error Scenarios

#### Execution Flow Errors:
1. **MaxTurnsExceeded**: `MaxTurnsExceeded(f"Max turns ({max_turns}) exceeded")`
   - Triggered when agent loop exceeds `max_turns` limit
   - Default limit is 10 turns

2. **Guardrail Failures**:
   - `InputGuardrailTripwireTriggered`: Input validation fails
   - `OutputGuardrailTripwireTriggered`: Output validation fails

3. **Model Errors**:
   - `ModelBehaviorError("Model did not produce a final response!")`: Model fails to respond
   - Model provider errors when invalid model string provided

#### Type Validation Errors:
1. **Invalid Agent**: `TypeError` if `starting_agent` is not Agent instance
2. **Invalid Input**: `TypeError` if `input` is not string or list of input items
3. **Invalid Context**: Runtime errors if context doesn't match agent's TContext type
4. **Invalid Hooks**: `TypeError` if hooks is not RunHooks instance

#### Configuration Errors:
1. **Invalid RunConfig**: `TypeError` if run_config is not RunConfig instance
2. **Invalid ModelProvider**: Errors when model_provider cannot resolve model
3. **Invalid Guardrails**: `TypeError` if guardrail lists contain invalid objects

### Best Practices for Error Prevention:
1. Always provide valid Agent instance for `starting_agent`
2. Ensure input is properly formatted (string or list of TResponseInputItem)
3. Set reasonable `max_turns` limit to prevent infinite loops
4. Validate guardrails before adding to lists
5. Use proper context types that match agent's TContext
6. Handle async/sync context appropriately (use `run_sync` only outside async contexts)

## Constants & Functions

| Name | Type | Value | Purpose |
|------|------|-------|---------|
| `DEFAULT_MAX_TURNS` | `int` | `10` | Default maximum execution turns |
| `DEFAULT_AGENT_RUNNER` | `AgentRunner` | `AgentRunner()` | Global default runner instance |

### Utility Functions:
| Function | Purpose | Parameters | Return Type |
|----------|---------|------------|-------------|
| `set_default_agent_runner()` | Set global default runner | `runner: AgentRunner \| None` | `None` |
| `get_default_agent_runner()` | Get global default runner | None | `AgentRunner` |

## Execution Flow Overview

### 1. Agent Run Loop:
```
1. Agent invoked with input
2. Check for final output (matches agent.output_type)
   - If yes: terminate with result
3. Check for handoff
   - If yes: switch to new agent, continue loop
4. Execute tool calls (if any)
5. Re-run loop until final output or max_turns exceeded
```

### 2. Error Handling Points:
- **Turn Limit**: MaxTurnsExceeded after max_turns
- **Input Validation**: InputGuardrailTripwireTriggered on first turn
- **Output Validation**: OutputGuardrailTripwireTriggered on final output
- **Model Errors**: ModelBehaviorError for invalid responses

### 3. Streaming vs Non-Streaming:
- **Non-streaming**: `run()` and `run_sync()` return complete RunResult
- **Streaming**: `run_streamed()` returns RunResultStreaming with event stream access

## Excel-Style Parameter Reference Table

| Class | Parameter | Type | Required | Default Value | Validation Rules | Error if Missing/Invalid |
|-------|-----------|------|----------|---------------|------------------|-------------------------|
| RunConfig | model | str\|Model\|None | ❌ | None | Valid model string or Model object | TypeError/ValueError on invalid model |
| RunConfig | model_provider | ModelProvider | ❌ | MultiProvider() | Valid ModelProvider instance | TypeError on invalid provider |
| RunConfig | model_settings | ModelSettings\|None | ❌ | None | Valid ModelSettings object | TypeError on invalid settings |
| RunConfig | handoff_input_filter | HandoffInputFilter\|None | ❌ | None | Valid filter function | TypeError on invalid filter |
| RunConfig | input_guardrails | list[InputGuardrail]\|None | ❌ | None | List of valid guardrails | TypeError on invalid guardrails |
| RunConfig | output_guardrails | list[OutputGuardrail]\|None | ❌ | None | List of valid guardrails | TypeError on invalid guardrails |
| RunConfig | tracing_disabled | bool | ❌ | False | Boolean value | TypeError on non-boolean |
| RunConfig | trace_include_sensitive_data | bool | ❌ | True | Boolean value | TypeError on non-boolean |
| RunConfig | workflow_name | str | ❌ | "Agent workflow" | Non-empty string | ValueError on empty string |
| RunConfig | trace_id | str\|None | ❌ | None | Valid string or None | TypeError on invalid type |
| RunConfig | group_id | str\|None | ❌ | None | Valid string or None | TypeError on invalid type |
| RunConfig | trace_metadata | dict[str,Any]\|None | ❌ | None | Valid dictionary or None | TypeError on invalid dict |
| RunOptions | context | TContext\|None | ❌ | None | Matches agent's context type | TypeError on type mismatch |
| RunOptions | max_turns | int | ❌ | DEFAULT_MAX_TURNS | Positive integer | TypeError on non-int, ValueError on negative |
| RunOptions | hooks | RunHooks\|None | ❌ | None | Valid RunHooks instance | TypeError on invalid hooks |
| RunOptions | run_config | RunConfig\|None | ❌ | None | Valid RunConfig instance | TypeError on invalid config |
| RunOptions | previous_response_id | str\|None | ❌ | None | Valid string or None | TypeError on invalid type |
| Runner.run | starting_agent | Agent[TContext] | ✅ | None | Valid Agent instance | TypeError: missing required argument |
| Runner.run | input | str\|list[TResponseInputItem] | ✅ | None | String or list of input items | TypeError: missing required argument |
| Runner.run | context | TContext\|None | ❌ | None | Matches agent's context type | TypeError on type mismatch |
| Runner.run | max_turns | int | ❌ | DEFAULT_MAX_TURNS | Positive integer | TypeError/ValueError on invalid |
| Runner.run | hooks | RunHooks\|None | ❌ | None | Valid RunHooks instance | TypeError on invalid hooks |
| Runner.run | run_config | RunConfig\|None | ❌ | None | Valid RunConfig instance | TypeError on invalid config |
| Runner.run | previous_response_id | str\|None | ❌ | None | Valid string or None | TypeError on invalid type |

## Object Relationship Mapping

| Parent Object | Child/Related Objects | Relationship Type | Usage Context |
|---------------|----------------------|-------------------|---------------|
| RunConfig | ModelProvider | Composition | Model resolution |
| RunConfig | ModelSettings | Association | Global model configuration |
| RunConfig | InputGuardrail | Aggregation | Input validation pipeline |
| RunConfig | OutputGuardrail | Aggregation | Output validation pipeline |
| RunConfig | HandoffInputFilter | Association | Input filtering for handoffs |
| RunOptions | RunConfig | Association | Configuration container |
| RunOptions | RunHooks | Association | Lifecycle event handling |
| Runner | AgentRunner | Delegation | Static methods delegate to default runner |
| AgentRunner | Agent | Usage | Executes agent instances |
| AgentRunner | RunConfig | Configuration | Run-time configuration |
| AgentRunner | RunHooks | Event Handling | Lifecycle callbacks |

## Method Comparison Table

| Method | Class | Async | Return Type | Use Case | Limitations |
|--------|-------|-------|-------------|----------|-------------|
| `Runner.run()` | Runner | ✅ Yes | RunResult | Async environments | Requires async context |
| `Runner.run_sync()` | Runner | ❌ No | RunResult | Sync environments | Cannot be used in async context |
| `Runner.run_streamed()` | Runner | ❌ No | RunResultStreaming | Real-time streaming | More complex to handle |
| `AgentRunner.run()` | AgentRunner | ✅ Yes | RunResult | Low-level control | Experimental API |
| `AgentRunner.run_sync()` | AgentRunner | ❌ No | RunResult | Sync low-level control | Experimental API |
| `AgentRunner.run_streamed()` | AgentRunner | ❌ No | RunResultStreaming | Streaming low-level control | Experimental API |

## Common Usage Patterns & Error Prevention

### 1. Basic Agent Execution
```python
# Async execution (recommended)
result = await Runner.run(
    starting_agent=my_agent,
    input="Hello, world!"
)  # ✅ Valid

# Sync execution (for non-async contexts)
result = Runner.run_sync(
    starting_agent=my_agent,
    input="Hello, world!"
)  # ✅ Valid

# Common errors
result = await Runner.run()  # ❌ TypeError: missing required arguments
result = Runner.run_sync(my_agent, "input")  # ❌ In async context
```

### 2. Configuration Setup
```python
# Proper configuration
config = RunConfig(
    max_turns=20,
    tracing_disabled=False,
    workflow_name="My Custom Workflow"
)

result = await Runner.run(
    starting_agent=my_agent,
    input="Hello",
    run_config=config
)  # ✅ Valid

# Common errors
config = RunConfig(
    max_turns=-1,  # ❌ ValueError: negative turns
    workflow_name="",  # ❌ ValueError: empty name
    model_provider="invalid"  # ❌ TypeError: not ModelProvider
)
```

### 3. Guardrail Configuration
```python
# Correct guardrail setup
input_guards = [MyInputGuardrail(), AnotherInputGuardrail()]
output_guards = [MyOutputGuardrail()]

config = RunConfig(
    input_guardrails=input_guards,
    output_guardrails=output_guards
)

# Common errors
config = RunConfig(
    input_guardrails=["not_a_guardrail"],  # ❌ TypeError: invalid guardrail
    output_guardrails=MyOutputGuardrail()  # ❌ TypeError: not a list
)
```

### 4. Streaming Usage
```python
# Correct streaming setup
streaming_result = Runner.run_streamed(
    starting_agent=my_agent,
    input="Stream this"
)

async for event in streaming_result.stream_events():
    print(f"Event: {event}")  # ✅ Valid

# Wait for completion
final_result = await streaming_result.get_result()
```

## Error Handling Strategies

### 1. Turn Limit Management
```python
try:
    result = await Runner.run(
        starting_agent=my_agent,
        input="Complex task",
        max_turns=50  # Increase for complex tasks
    )
except MaxTurnsExceeded as e:
    print(f"Agent exceeded turn limit: {e}")
    # Handle gracefully - maybe retry with higher limit
```

### 2. Guardrail Error Handling
```python
try:
    result = await Runner.run(
        starting_agent=my_agent,
        input=user_input,
        run_config=config_with_guardrails
    )
except InputGuardrailTripwireTriggered as e:
    print(f"Input validation failed: {e.result.guardrail.get_name()}")
    # Handle invalid input
except OutputGuardrailTripwireTriggered as e:
    print(f"Output validation failed: {e.result.guardrail.get_name()}")
    # Handle invalid output
```

### 3. Model Error Handling
```python
try:
    result = await Runner.run(
        starting_agent=my_agent,
        input="Generate response"
    )
except ModelBehaviorError as e:
    print(f"Model failed to respond properly: {e}")
    # Retry or use fallback
except AgentsException as e:
    print(f"General agent error: {e}")
    # Access error details: e.run_data
```

### 4. Context Type Safety
```python
# Ensure context matches agent's expected type
class MyContext:
    def __init__(self, user_id: str):
        self.user_id = user_id

my_agent: Agent[MyContext] = Agent(name="typed_agent")
context = MyContext("user123")

result = await Runner.run(
    starting_agent=my_agent,
    input="Hello",
    context=context  # ✅ Type matches
)

# Avoid type mismatches
wrong_context = "string_context"  # ❌ Type mismatch
```

## Performance Considerations

### 1. Async vs Sync
- **Use `run()`** for async environments (FastAPI, async frameworks)
- **Use `run_sync()`** only in pure sync environments (scripts, sync frameworks)
- **Never use `run_sync()`** inside async functions or Jupyter notebooks

### 2. Streaming Benefits
- Real-time response processing
- Lower memory usage for long conversations
- Better user experience with progressive updates
- More complex error handling required

### 3. Turn Optimization
- Set appropriate `max_turns` based on task complexity
- Monitor turn usage to optimize agent design
- Use guardrails to prevent infinite loops
- Consider tool usage patterns when setting limits
