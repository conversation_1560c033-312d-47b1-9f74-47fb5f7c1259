import os
from dotenv import load_dotenv
from agents import Agent, <PERSON>, <PERSON>H<PERSON><PERSON>, RunConfig
from typing import Any
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")

# agent = Agent(
#     name="Assistant",
#     instructions="You are helpful Assistant Respond Politely And Clearly"
#     )
# context= {"user_id":"12345"}

# class MyHook(RunHooks[dict]):
#     def on_agent_start(self, agent: Agent ,input_data: Any, context: dict)-> None:
#         print(f"[Hook] Agent {agent.name} is starting with input: {input_data}")
#     def on_agent_end(self, agent: Agent ,result: Any, context: dict)-> None:
#         print(f"[Hook] Agent {agent.name} ended with result: {result.final_output}")

# run_config = RunConfig()

# result = Runner.run_sync(
#     starting_agent=agent,
#     input="write a Poem of 1 line",
#     context=context,
#     max_turns=5,
#     hooks=MyHook(),
#     run_config=run_config,
#     previous_response_id=2
# )
# print("\n---Final Output --\n")
# print(result.final_output)
# print("\n---Final Output --\n")
# print(result)

# Code within the code,
# Functions calling themselves,
# Infinite loop's dance.

from agents import Agent, Runner, RunConfig, RunHooks
from typing import Any

# Define the agent
agent = Agent(
    name="Assistant",
    instructions="You are a helpful Assistant. Respond Politely and Clearly."
)

# Context dictionary
context = {"user_id": "12345"}

# Custom hook class with asynchronous methods
class MyHook(RunHooks[dict]):
    async def on_agent_start(self, context: dict, agent: Agent) -> None:
        print(f"[Hook] Agent {agent.name} is starting with context: {context}")

    async def on_agent_end(self, context: dict, agent: Agent, result: Any) -> None:
        # Print the result directly, as it is the final output string
        print(f"[Hook] Agent {agent.name} ended with result: {result}")

# Run configuration
run_config = RunConfig()

# Execute the runner synchronously
result = Runner.run_sync(
    starting_agent=agent,
    input="Write a poem of 1 line",
    context=context,
    max_turns=5,
    hooks=MyHook(),
    run_config=run_config,
    previous_response_id=None
)

# Print the results
print("\n--- Final Output ---\n")
print(result.final_output)
print("\n--- RunResult ---\n")
print(result)