import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")



@function_tool
def get_weather(city: str):
    return f"The weather in {city} is sunny"

# @function_tool
# def get_weather(city):  # city: str is missing
#     return f"The weather in {city} is sunny"

# @function_tool
# def get_weather(city: str, temperature: int = 30, *args) -> str: 
#     return f"{city} is {temperature}°C"

agent = Agent(
    name="WeatherAgent",
    instructions="Responds with weather.",
    tools=[get_weather],  # This will raise an error
)

async def main():
    result  = await Runner.run(agent,"What is the weather in San Francisco?")
    print("\n--- Final Output ---\n")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())
