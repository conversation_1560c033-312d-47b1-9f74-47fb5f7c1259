import os
from dotenv import load_dotenv
from agents import Agent, InputGuardrail, GuardrailFunctionOutput, Runner, RunContextWrapper, function_tool, handoff
from agents.extensions import handoff_filters
from dataclasses import dataclass
import asyncio
from pydantic import BaseModel
load_dotenv()

OPENAI_API_KEY=os.getenv("OPENAI_API_KEY")


# billing_agent = Agent(name="Billing agent")
# external_refund_agent = Agent(name="Refund agent")
@function_tool
def weather_report(location: str):
    return f"The weather in {location} is sunny."

refund_agent = Agent(name="Refund agent",
                     instructions=None,
                     tools=[],
                     handoffs=[])

general_agent = Agent(name="General agent",
                      instructions=None,
                      tools=[weather_report],
                      handoffs=[
    handoff(agent=refund_agent,
            tool_name_override="refund",
            tool_description_override="handles a rfund request",
            input_filter=handoff_filters.remove_all_tools
            )
])

async def main():
    result  = await Runner.run(
        general_agent,"what is a weather in mumbai? I want to refund my order")
    print("\n--- Final Output ---\n")
    print(result.final_output)
    print(result.last_agent.name)

if __name__ == "__main__":
    asyncio.run(main())
